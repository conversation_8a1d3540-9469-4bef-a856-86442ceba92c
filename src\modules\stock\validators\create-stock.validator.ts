import { z } from "zod";

const optionalNumberPreprocess = z.preprocess(val => {
	if (typeof val === "string") {
		const parsed = parseFloat(val);
		return isNaN(parsed) ? undefined : parsed;
	}
	if (typeof val === "number") {
		return isNaN(val) ? undefined : val;
	}
	return val;
}, z.number().optional());

export const supplierSchema = z.object({
	id: z.number(),
	name: z.string(),
});

export const invoiceSchema = z.object({
	key: z.string(),
	issueDate: z.string(),
	supplier: supplierSchema,
});

export const packageSchema = z
	.object({
		name: z.string().min(1, "O nome da caixa é obrigatório"),
		barcode: z.string().min(1, "O código de barras da caixa é obrigatório"),
		code: z.string().min(1, "O código da caixa é obrigatório"),
		quantityPerPackage: z.preprocess(
			val => {
				if (typeof val === "string") {
					const parsed = parseFloat(val);
					return isNaN(parsed) ? undefined : parsed;
				}
				return val;
			},
			z.number().min(1, "A quantidade por caixa deve ser maior que 0")
		),
		id: z.number().optional(),
	})
	.refine(
		data => {
			if (data.id) return true;
			return Boolean(data.name && data.barcode && data.code && data.quantityPerPackage);
		},
		{
			message: "Os campos da caixa são obrigatórios para novos pacotes",
			path: ["name"],
		}
	);

const pricePreprocess = z.preprocess(
	val => {
		if (typeof val === "string") {
			const parsed = parseFloat(val);
			console.log("Price preprocess input:", { val, parsed, type: typeof parsed });
			return isNaN(parsed) ? undefined : parsed;
		}
		if (typeof val === "number") {
			return isNaN(val) ? undefined : val;
		}
		return val;
	},
	z.union([z.number().min(0, "O preço deve ser maior ou igual a zero"), z.undefined(), z.null()]).optional()
);

const productBaseObject = z.object({
	id: z.number().optional(),
	name: z.string(),
	barcode: z.string(),
	code: z.string(),
	price: pricePreprocess,
	costPrice: pricePreprocess,
	ncm: z.string(),
});

export const productBaseSchema = productBaseObject.refine(
	data => {
		// Se o produto tem ID (já existe), aceita qualquer valor de preço
		if (data.id) return true;
		// Se não tem ID (produto novo), preços são obrigatórios e devem ser > 0
		return typeof data.price === "number" && typeof data.costPrice === "number" && data.price > 0 && data.costPrice > 0;
	},
	{
		message: "Preços são obrigatórios e devem ser maiores que zero para produtos novos",
		path: ["price"],
	}
);

export const productDetailsSchema = productBaseObject
	.extend({
		description: z.string(),
		categoryId: z.number().optional(),
		package: packageSchema,
	})
	.refine(
		data => {
			// Se o produto tem ID (já existe), aceita qualquer valor de preço
			if (data.id) return true;
			// Se não tem ID (produto novo), preços são obrigatórios e devem ser > 0
			return typeof data.price === "number" && typeof data.costPrice === "number" && data.price > 0 && data.costPrice > 0;
		},
		{
			message: "Preços são obrigatórios e devem ser maiores que zero para produtos novos",
			path: ["price"],
		}
	);

export const stockMovementSchema = z.object({
	description: z.string().optional(),
	quantity: optionalNumberPreprocess,
	product: productDetailsSchema,
	packageQuantity: optionalNumberPreprocess,
});

export const inventorySchema = z.object({
	id: z.number().optional(),
	expirationDate: z.string().optional(),
	stockMovement: stockMovementSchema,
});

export const createStockDtoSchema = z.object({
	invoice: invoiceSchema,
	inventories: z.array(inventorySchema),
});

export type ICreateStock = z.infer<typeof createStockDtoSchema>;
